import React, { useEffect } from "react";
import { ListItem, ListItemIcon, ListItemText } from "@mui/material";
import { styled } from "@mui/material/styles";
import CustomTooltip from "../../components/menu/CustomTooltip";

interface ElementoListaProps {
  icon: React.ReactNode;
  open: boolean;
  text: string;
  onClick: () => void;
  selected: boolean;
  tooltipText: string;
  disableSelectedColor?: boolean;
  customStyle?: React.CSSProperties;
}

const CustomListItemText = styled(ListItemText)(({ theme }) => ({
  "& .MuiListItemText-primary": {
    fontFamily: "Inter, sans-serif", // Cambiado a Inter
    fontSize: "1rem",
    color: theme.palette.text.primary,
  },
}));

export default function ElementoLista({
  icon,
  open,
  text,
  onClick,
  selected,
  tooltipText,
  disableSelectedColor = false,
  customStyle,
}: ElementoListaProps) {
  return (
    <CustomTooltip title={tooltipText} placement="right" arrow>
      <ListItem
        button
        selected={selected}
        onClick={onClick}
        sx={{
          padding: "12px 16px",
          minHeight: "56px",
          backgroundColor: selected
            ? disableSelectedColor
              ? "transparent"
              : "inherit"
            : "inherit",
          "&.Mui-selected": {
            backgroundColor: "#F2F2F2",
            "& .MuiListItemText-primary": {
              color: disableSelectedColor ? "inherit" : "#2E7D32",
              fontFamily: "Inter, sans-serif", // Cambiado a Inter
              transition: "color 0.3s ease",
            },
            transition: "background-color 0.3s ease",
          },
          cursor: "pointer",
          "&:hover": { backgroundColor: "#F0F0F0" },
          fontFamily: "Inter, sans-serif", // Cambiado a Inter
          ...customStyle,
        }}
      >
        <ListItemIcon
          sx={{ fontFamily: "Inter, sans-serif", fontSize: "24px" }} // Cambiado a Inter
        >
          {icon}
        </ListItemIcon>
        {open && <CustomListItemText primary={text} />}
      </ListItem>
    </CustomTooltip>
  );
}
