import React from "react";
import PropTypes from "prop-types";
import {
  Card,
  CardContent,
  Box,
  Typography,
  Avatar,
  IconButton,
  Tooltip,
  Grid,
  useTheme,
  Skeleton,
  SxProps,
  Theme,
} from "@mui/material";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import { LineChart, Line, ResponsiveContainer } from "recharts";

// TypeScript interfaces
interface SparklineDataPoint {
  value: number;
  label?: string;
}

interface KpiCardProps {
  title: string;
  value?: string | number;
  delta?: number;
  caption?: string;
  icon?: React.ReactNode;
  sparklineData?: SparklineDataPoint[];
  loading?: boolean;
  onClick?: () => void;
  sx?: SxProps<Theme>;
}

/**
 * KpiCard
 * Props:
 *  - title: string
 *  - value: string | number
 *  - delta: number (percent change, positive or negative)
 *  - caption: small text under value (string)
 *  - icon: React node (optional)
 *  - sparklineData: [{ value: number, label?: string }] (optional)
 *  - loading: bool
 *  - onClick: function (optional)
 *  - sx: additional sx styles
 */
export function KpiCard({
  title,
  value,
  delta,
  caption,
  icon,
  sparklineData,
  loading,
  onClick,
  sx,
}: KpiCardProps) {
  const theme = useTheme();
  const positive = typeof delta === "number" ? delta >= 0 : null;

  return (
    <Card
      elevation={2}
      sx={{
        cursor: onClick ? "pointer" : "default",
        height: "100%",
        display: "flex",
        flexDirection: "column",
        ...sx,
      }}
      onClick={onClick}
    >
      <CardContent
        sx={{ display: "flex", flexDirection: "column", gap: 1, flex: 1 }}
      >
        <Box
          display="flex"
          justifyContent="space-between"
          alignItems="flex-start"
        >
          <Box>
            <Typography variant="subtitle2" color="text.secondary" noWrap>
              {title}
            </Typography>
            {loading ? (
              <Skeleton variant="text" width={120} height={36} />
            ) : (
              <Typography variant="h5" sx={{ mt: 0.5, fontWeight: 600 }}>
                {value}
              </Typography>
            )}
            {caption && !loading && (
              <Typography
                variant="caption"
                color="text.secondary"
                display="block"
              >
                {caption}
              </Typography>
            )}
          </Box>

          <Box display="flex" alignItems="center" gap={1}>
            {icon && (
              <Avatar
                variant="rounded"
                sx={{
                  width: 40,
                  height: 40,
                  bgcolor: theme.palette.action.hover,
                }}
              >
                {icon}
              </Avatar>
            )}
            <Tooltip title="Más info">
              <IconButton size="small">
                <InfoOutlinedIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* delta row + sparkline */}
        <Box
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          mt={1}
        >
          <Box display="flex" alignItems="center" gap={0.5}>
            {typeof delta === "number" ? (
              <Box display="flex" alignItems="center" gap={0.5}>
                {positive ? (
                  <ArrowUpwardIcon
                    sx={{ fontSize: 18, color: "success.main" }}
                  />
                ) : (
                  <ArrowDownwardIcon
                    sx={{ fontSize: 18, color: "error.main" }}
                  />
                )}
                <Typography
                  variant="caption"
                  sx={{
                    color: positive ? "success.main" : "error.main",
                    fontWeight: 600,
                  }}
                >
                  {Math.abs(delta).toFixed(1)}%
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  vs prev
                </Typography>
              </Box>
            ) : (
              <Typography variant="caption" color="text.secondary">
                &nbsp;
              </Typography>
            )}
          </Box>

          <Box sx={{ width: 120, height: 40 }}>
            {sparklineData && sparklineData.length > 1 && (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={sparklineData}
                  margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
                >
                  <Line
                    type="monotone"
                    dataKey="value"
                    stroke={theme.palette.primary.main}
                    strokeWidth={2}
                    dot={false}
                  />
                </LineChart>
              </ResponsiveContainer>
            )}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
}

KpiCard.propTypes = {
  title: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  delta: PropTypes.number,
  caption: PropTypes.string,
  icon: PropTypes.node,
  sparklineData: PropTypes.array,
  loading: PropTypes.bool,
  onClick: PropTypes.func,
  sx: PropTypes.object,
};

interface KpiGridItem extends KpiCardProps {
  id: string;
}

interface KpiGridColumns {
  xs?: number;
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
}

interface KpiGridProps {
  items?: KpiGridItem[];
  columns?: KpiGridColumns;
}

/**
 * KpiGrid: simple responsive grid to layout KPI cards
 * Props:
 *  - items: array of { id, title, value, delta, caption, icon, sparklineData, loading }
 *  - columns: responsive columns object (optional)
 */
export function KpiGrid({
  items = [],
  columns = { xs: 12, sm: 6, md: 4 },
}: KpiGridProps) {
  return (
    <Grid container spacing={2}>
      {items.map((item) => (
        <Grid
          item
          key={item.id}
          xs={columns.xs}
          sm={columns.sm}
          md={columns.md}
        >
          <KpiCard {...item} />
        </Grid>
      ))}
    </Grid>
  );
}

KpiGrid.propTypes = {
  items: PropTypes.array,
  columns: PropTypes.object,
};

/**
 * Demo / Example usage default export
 * - Provides example KPIs and shows how to import/use KpiGrid
 */
export default function KpiComponentsDemo() {
  const mockSpark = (start = 10) =>
    Array.from({ length: 10 }).map((_, i) => ({
      value: Math.round((start + Math.sin(i / 2) * 3 + i * 0.5) * 10) / 10,
    }));

  const demoItems = [
    {
      id: "ingresos",
      title: "Ingresos (mes)",
      value: "$ 1.250.000",
      delta: 8.2,
      caption: "vs mes anterior",
      sparklineData: mockSpark(100),
    },
    {
      id: "hectareas",
      title: "Hectáreas atendidas",
      value: "1.230 ha",
      delta: -2.4,
      caption: "últimos 30 días",
      sparklineData: mockSpark(50),
    },
    {
      id: "utilizacion",
      title: "Utilización maquinaria",
      value: "72 %",
      delta: 4.6,
      caption: "promedio flota",
      sparklineData: mockSpark(70),
    },
    {
      id: "costohora",
      title: "Costo por hora",
      value: "$ 3.800",
      delta: 1.1,
      caption: "comb, mano de obra",
      sparklineData: mockSpark(30),
    },
    {
      id: "ontime",
      title: "Trabajos a tiempo",
      value: "91 %",
      delta: 0.8,
      caption: "a tiempo",
      sparklineData: mockSpark(90),
    },
    {
      id: "mantenimiento",
      title: "Mantenimientos próximos",
      value: "3 máquinas",
      caption: "en los próximos 7 días",
      sparklineData: mockSpark(20),
    },
  ];

  return (
    <Box p={2}>
      <Typography variant="h6" sx={{ mb: 2 }}>
        KPIs reutilizables — demo
      </Typography>
      <KpiGrid items={demoItems} />

      <Box mt={3}>
        <Typography variant="body2" color="text.secondary">
          Consejos:
        </Typography>
        <ul>
          <li>
            <Typography variant="body2">
              Pasa datos reales desde tu API y actualiza los props:{" "}
              <code>value</code>, <code>delta</code> y{" "}
              <code>sparklineData</code>.
            </Typography>
          </li>
          <li>
            <Typography variant="body2">
              Para sparklines puedes usar datos de los últimos 7/14/30 puntos
              (día/semana) según tu granularidad.
            </Typography>
          </li>
          <li>
            <Typography variant="body2">
              Si usas TypeScript simplemente tipa las props y exporta los
              componentes.
            </Typography>
          </li>
        </ul>
      </Box>
    </Box>
  );
}
