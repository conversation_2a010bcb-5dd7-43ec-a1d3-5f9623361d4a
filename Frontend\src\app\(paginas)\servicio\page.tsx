"use client";
import React, { SetStateAction, useRef, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Grid,
  TextField,
  ToggleButtonGroup,
  ToggleButton,
  Paper,
  Tabs,
  Tab,
  Card,
  CardHeader,
  Avatar,
  CardContent,
  InputAdornment,
  IconButton,
  Dialog,
  DialogContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  CircularProgress,
} from "@mui/material";
import Image from "next/image";
import FormattedInput from "../../components/valuesFormat/FormattedInput";
import {
  validateForm,
  customValidations,
} from "../../../utiles/validarFormFields";
import { Box } from "@mui/system";
import { useEffect } from "react";
import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import SearchIcon from "@mui/icons-material/Search";
import CloseIcon from "@mui/icons-material/Close";
import AgricultureIcon from "@mui/icons-material/Agriculture";
import Chip from "@mui/material/Chip";
import EditIcon from "@mui/icons-material/Edit";
import { Inter } from "next/font/google";
import { DialogTitle } from "@mui/material";
import { DialogContentText } from "@mui/material";
import AddCircleIcon from "@mui/icons-material/AddCircle";

const inter = Inter({ subsets: ["latin"] });
// First, define the interface for the categoria type
interface Categoria {
  categoria: string;
  icon: JSX.Element;
  subServicios: Array<{
    nombre: string;
    icon: JSX.Element;
    unidad?: string; // Added unidad property as optional
  }>;
}

// Define interface for service data structure
interface Servicio {
  id: number;
  servcio: string;
  rindeQQ?: string | number;
  monto: number;
  unidad: string;
}

// Define interface for column structure
interface Column {
  field: string;
  headerName: string;
  width: number;
}

const Servicios = ({}) => {
  const [categoriaSeleccionada, setCategoriaSeleccionada] =
    useState<Categoria | null>(null);
  const [subServicioSeleccionado, setSubServicioSeleccionado] = useState("");
  const [filteredRows, setFilteredRows] = useState([]);
  const [estadoModal, setEstadoModal] = useState<"add" | "update">("add");
  const [isSearchBarOpen, setIsSearchBarOpen] = useState(false);
  const [open, setOpen] = useState(false);
  const [rows, setRows] = useState<Servicio[]>([]);
  const [selectedRow, setSelectedRow] = useState(null);
  const [isValidName, setIsValidName] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [tabValue, setTabValue] = useState(0);
  const [searchTerm, setSearchTerm] = useState("");
  const selectServicioRef = useRef<HTMLInputElement | HTMLSelectElement>(null);
  const selectSubServicioRef = useRef<HTMLInputElement | HTMLSelectElement>(
    null
  );

  useEffect(() => {
    // Simular carga de datos
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 4000); // 2 segundos de simulación
    return () => clearTimeout(timer);
  }, []);

  const [formData, setFormData] = useState({
    serviceName: "",
    subServiceName: "",
    subServiceRindeQQ: "",
    subServicePrice: "",
    subServiceUnite: "",
  });

  const handleOpenAdd = () => {
    setEstadoModal("add");
    clearForm();
    setOpen(true);
  };

  const initialErrorState = {
    serviceName: "",
    subServiceName: "",
    subServiceRindeQQ: "",
    subServicePrice: "",
    subServiceUnite: "",
  };
  const [error, setError] = useState(initialErrorState);

  const clearForm = () => {
    setFormData({
      serviceName: "",
      subServiceName: "",
      subServiceRindeQQ: "",
      subServicePrice: "",
      subServiceUnite: "",
    });
    setError(initialErrorState);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    if (!/^[0-9]*$/.test(value)) {
      return;
    }

    // Limitar a 3 caracteres
    if (value.length > 4) {
      return;
    }

    // Actualizar el estado con el valor ingresado
    setFormData({
      ...formData,
      [name]: value,
    });

    // Validar el campo al cambiar el valor
    const errorMessage = validateField(value, name);
    setError({
      ...error,
      [name]: errorMessage,
    });

    // Comprobar si el campo es válido
    setIsValidName(errorMessage === "");
  };

  const validateField = (value: string, name: string) => {
    const cleanedValue = value.trim(); // Limpiar espacios extra

    if (name === "subServiceRindeQQ") {
      if (!customValidations["numeros"].valid(cleanedValue)) {
        return customValidations["numeros"].error;
      }
    }

    return "";
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validar todo el formulario antes de enviarlo
    const formErrors = validateForm(formData, customValidations);

    if (formErrors) {
      setError(formErrors);
    } else {
      // Enviar datos o realizar otra acción si no hay errores
      console.log("Formulario válido, enviando datos...");
    }
  };

  const handleClickClose = (
    _event: React.MouseEvent<HTMLElement>,
    reason?: string
  ) => {
    if (reason && reason === "backdropClick") return;
    setOpen(false);
  };

  const handleSearchClick = (): void => {
    setIsSearchBarOpen(!isSearchBarOpen);
  };

  const handleListItemClick = (nombre: string): void => {
    setSubServicioSeleccionado(nombre); // Actualiza el estado con el nombre del subservicio seleccionado
  };

  const handleAmountChange = (value: string): void => {
    setFormData((prevFormData) => ({
      ...prevFormData,
      subServicePrice: value,
    }));
    console.log("Monto formateado:", value);
  };

  const handleCategoriaChange = (categoria: Categoria): void => {
    setCategoriaSeleccionada(categoria);
    setSubServicioSeleccionado("");
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // At the top of your component, define the base columns with fixed widths
  const baseColumns: Column[] = [
    { field: "servcio", headerName: "Servcio", width: 285 },
    { field: "monto", headerName: "Costo Estimado", width: 50 },
    { field: "unidad", headerName: "Unidad de Medida", width: 50 },
  ];

  const serviciosData = [
    {
      categoria: "Labranza - Protección",

      subServicios: [
        {
          nombre: "Subsolador 0.20 m.",
        },
        {
          nombre: "Subsolador 0.25 m.",
        },
        {
          nombre: "Arado Cincel Única Pesada",
        },
        {
          nombre: "Arado Cincel Rastrojero",
        },
        {
          nombre: "Rasta Disco Pesada s/potrero",
        },
        {
          nombre: "R. Dientes, Rabasto y Rolo",
        },
        {
          nombre: "Rolo Picador de Rastrojo",
        },
        {
          nombre: "Cultivador de Campo",
        },
        {
          nombre: "Rotorastra",
        },
        {
          nombre: "Carpidor",
        },
        {
          nombre: "Aplic. Fitosanitarios Arrastre -80 l/ha",
        },
        {
          nombre: "Aplic. Fitosanitarios Arrastre +80 l/ha",
        },

        {
          nombre: "Fertilización al Voleo Arrastre",
        },
        {
          nombre: "Fertilización Incorporada",
        },
        {
          nombre: "Desmalezadora 4.5m",
        },
      ],
    },

    {
      categoria: "Siembra",

      subServicios: [
        {
          nombre: "c/Fertilización Simple",
        },
        {
          nombre: "c/Fertilización Simple Neumática",
        },
        {
          nombre: "c/Fertilización Lateral",
        },
        {
          nombre: "c/Fertilización Lateral Neumáticalo",
        },
        {
          nombre: "c/Fertilización Doble",
        },
        {
          nombre: "c/Fertilización Neumática",
        },
        {
          nombre: "c/Fertilización Neumática c/cortes",
        },
      ],
    },

    {
      categoria: "Aplicacíon Líquidos",

      subServicios: [
        {
          nombre: "Fitosanitarios Líquidos Volumen -80lts/ha",
        },
        {
          nombre: "Fitosanitarios Líquidos Volumen +80lts/ha",
        },
        {
          nombre: "Fitosanitarios Líquidos Volumen -150lts/ha",
        },
        {
          nombre: "Fitosanitarios Líquidos Volumen +250lts/ha",
        },
        {
          nombre: "Aplicación Selectiva",
        },
        {
          nombre: "Granulados Fertilizantes Dosis 330kg/ha",
        },
        {
          nombre: "Polvo Yeso o Cal Dosis 400kg/ha",
        },
      ],
    },

    {
      categoria: "Aplicacíon Solidos",

      subServicios: [
        {
          nombre: "Granulados Dispersión por Platos 100kg/ha",
        },
        {
          nombre: "Granulados Dispersión por Platos 200kg/ha",
        },
        {
          nombre: "Granulados Neumática 100kg/ha",
        },
        {
          nombre: "Granulados Neumática 200kg/ha",
        },
        {
          nombre: "Polvos Dosis 400kg/ha",
        },
      ],
    },

    {
      categoria: "Henificacíon",

      subServicios: [
        {
          nombre: "Segadora+Rastrillo+Arrolladora 1.20 hilo",
        },
        {
          nombre: "Segadora+Rastrillo+Arrolladora 1.20 malla",
        },
        {
          nombre: "Segadora+Rastrillo+Arrolladora 1.50 hilo",
        },
        {
          nombre: "Segadora+Rastrillo+Arrolladora 1.50 malla",
        },
        {
          nombre: "Segadora+Rastrillo+Arrolladora 90ha ",
        },
        {
          nombre: "Sega-Acondicionadora+Rastrillo Giroscopico+MegaEnfardadora",
        },
      ],
    },

    {
      categoria: "Cosecha",

      subServicios: [
        {
          nombre: "Cosechadora Desglose Soja",
        },
        {
          nombre: "Cosechadora Desglose Sorgo",
        },
        {
          nombre: "Cosechadora Desglose Girasol",
        },
        {
          nombre: "Cosechadora Desglose Maiz",
        },
        {
          nombre: "Cosechadora Desglose Trigo-Avena-Cebada",
        },
        {
          nombre: "Cosechadora Desglose Lino-Colza-Apliste",
        },
      ],
    },
  ];

  // Eliminamos datosEjemplo ya que usamos datos reales de la base de datos

  useEffect(() => {
    setIsLoading(true);
    setRows([] as Servicio[]);
    setIsLoading(false);
  }, []); // Asegúrate de que solo se ejecute una vez

  useEffect(() => {
    console.log("Estado actual de rows:", rows);
    console.log("Estado de loading:", isLoading);
  }, [rows, isLoading]);

  //AGREGAR SERVICIO
  const handleAddServicio = async () => {
    const servicio = `${formData.serviceName} - ${formData.subServiceName}`;
    const newService = {
      servcio: servicio, // Asegúrate de que el nombre del campo coincida con la interfaz Servicio
      monto: Number(formData.subServicePrice),
      unidad: formData.subServiceUnite,
      // Si es cosecha, incluir el rindeQQ
      ...(categoriaSeleccionada?.categoria === "Cosecha" && {
        rindeQQ: formData.subServiceRindeQQ,
      }),
    };

    const errors = validateForm(formData);
    if (errors) {
      setError(errors);
      return;
    }

    try {
      const res = await fetch("http://localhost:8080/api/servicio", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(newService),
      });

      if (!res.ok) {
        throw new Error("Error al guardar el servicio");
      }

      // Obtener los datos actualizados inmediatamente después de guardar
      const resGet = await fetch("http://localhost:8080/api/servicio");
      if (!resGet.ok) {
        throw new Error("Error al obtener los servicios actualizados");
      }
      const dataServicios = await resGet.json();
      setRows(dataServicios);

      // Limpiar el formulario y cerrar el modal
      clearForm();
      setOpen(false);

      // Cambiar a la pestaña correspondiente al servicio agregado
      const newTabIndex = serviciosData.findIndex(
        (s) => s.categoria === formData.serviceName
      );
      if (newTabIndex !== -1) {
        setTabValue(newTabIndex);
      }
    } catch (error) {
      console.error("Error en la solicitud:", error);
    }
  };

  //CARGAR SERVICIO EN EL DATAGRID
  const fetchServicios = async () => {
    try {
      const res = await fetch("http://localhost:8080/api/servicio");
      if (!res.ok) {
        throw new Error("Error al obtener los servicios");
      }
      const dataServicios = await res.json();
      return dataServicios;
    } catch (error) {
      console.error("Error en la solicitud:", error);
      // Devolver un valor predeterminado en caso de error
      return [];
    }
  };

  useEffect(() => {
    const getData = async () => {
      setIsLoading(true);
      try {
        const res = await fetch("http://localhost:8080/api/servicio");
        if (!res.ok) {
          console.log("API no disponible");
          setRows([]);
          return;
        }
        const dataServicios = await res.json();
        setRows(dataServicios);
      } catch (error) {
        console.log("Error al obtener datos:", error);
        setRows([]);
      } finally {
        setIsLoading(false);
      }
    };

    getData();
  }, []); // Solo se ejecuta una vez al montar el componente

  //BUSCAR SERVICIO
  const handleSearchServicio = async (value: string): Promise<void> => {
    const filteredData = rows.filter((row: Servicio) => {
      // Convert all values to strings and handle possible undefined values
      const rindeQQStr = row.rindeQQ?.toString() || "";
      const montoStr = row.monto?.toString() || "";
      const searchValue = value.toLowerCase();

      return (
        row.servcio.toLowerCase().includes(searchValue) ||
        rindeQQStr.toLowerCase().includes(searchValue) ||
        montoStr.toLowerCase().includes(searchValue) ||
        row.unidad.toLowerCase().includes(searchValue)
      );
    });

    setFilteredRows(filteredData as SetStateAction<typeof filteredRows>);
  };

  //CLICK BOTON MODIFICAR(LAPIZ)
  const handleEdit = async (id: number) => {
    try {
      const res = await fetch(`http://localhost:8080/api/servicio/${id}`, {
        method: "GET",
      });

      if (res.ok) {
        console.log("Servicios obtenido exitosamente.");

        const service = await res.json();
        setFormData({
          serviceName: service.nombre,
          subServiceName: service.subServicio,
          subServiceRindeQQ: service.RindeQQ,
          subServicePrice: service.precio,
          subServiceUnite: service.unidad,
        });
      } else {
        console.error("Error al modificar el servicio:", id);
      }
    } catch (error) {
      console.error("Error en la solicitud de eliminación:", error);
    }

    setEstadoModal("update");
    setOpen(true);
  };

  //MODIFICAR SERVICIO PARA GAURDAR
  const handleUpdateServicio = async () => {
    if (!selectedRow) return;

    // Crear el objeto de servicio actualizado
    const updatedService = {
      servcio: `${formData.serviceName} - ${formData.subServiceName}`,
      monto: Number(formData.subServicePrice),
      unidad: formData.subServiceUnite,
      ...(formData.serviceName === "Cosecha" && {
        rindeQQ: formData.subServiceRindeQQ,
      }),
    };

    try {
      const res = await fetch(
        `http://localhost:8080/api/servicio/${
          (selectedRow as { id: number }).id
        }`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(updatedService),
        }
      );

      if (!res.ok) {
        throw new Error("Error al actualizar el servicio");
      }

      // Obtener la lista actualizada de servicios
      const resGet = await fetch("http://localhost:8080/api/servicio");
      if (!resGet.ok) {
        throw new Error("Error al obtener los servicios actualizados");
      }
      const dataServicios = await resGet.json();
      setRows(dataServicios);

      // Limpiar el formulario y cerrar el modal
      clearForm();
      setOpen(false);
    } catch (error) {
      console.error("Error en la actualización:", error);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    const key = e.key;

    // Si la tecla presionada no es un número, evitarla
    if (!/[0-9]/.test(key)) {
      e.preventDefault();
      return;
    }
  };

  const [unidad, setUnidad] = useState<string>("");

  const handleChange = (
    event: React.MouseEvent<HTMLElement>,
    newUnidad: string
  ) => {
    if (newUnidad !== null) {
      setUnidad(newUnidad);
      // Actualizar el formData con la nueva unidad seleccionada
      setFormData((prev) => ({
        ...prev,
        subServiceUnite: newUnidad,
      }));
    }
  };

  const toggleButtonStyles = {
    border: "1px solid #ddd",
    color: "#333",
    "&.Mui-selected": {
      backgroundColor: "#1976d2",
      color: "#fff",
    },
    "&:hover": {
      backgroundColor: "#f0f0f0",
    },
  };

  const labelStyles = {
    fontWeight: 600,
    color: "#333",
    marginBottom: "8px",
    display: "block",
    fontFamily: "Lexend, sans-serif",
  };

  return (
    <>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          mb: 3,
          mt: 3,
        }}
      >
        <Box>
          <Typography
            variant="h4"
            component="div"
            sx={{ fontWeight: "bold", fontFamily: "Lexend, sans-serif" }}
          >
            Servicios Agricolas
          </Typography>
          <Typography
            variant="subtitle1"
            sx={{
              color: "text.secondary",
              mt: 1,
              fontFamily: `${inter.style.fontFamily}`,
            }}
          >
            Gestione los Servicios Agricolas
          </Typography>
        </Box>
        <Button
          variant="contained"
          onClick={handleOpenAdd}
          sx={{
            bgcolor: "#2E7D32", // Color verde más oscuro
            color: "#ffffff",
            "&:hover": { bgcolor: "#0D9A0A" },
            height: "fit-content",
            alignSelf: "center",
            fontFamily: `${inter.style.fontFamily}`, // Agregado para usar Inter
          }}
          startIcon={<AddOutlinedIcon />}
        >
          Nuevo Servicio
        </Button>
      </Box>
      <TextField
        fullWidth
        variant="outlined"
        placeholder="Buscar..."
        value={searchTerm}
        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
          handleSearchServicio(e.target.value)
        }
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <IconButton onClick={handleSearchClick}>
                <SearchIcon />
              </IconButton>
            </InputAdornment>
          ),
          style: { fontFamily: "Inter, sans-serif" },
        }}
        sx={{
          mb: 2,
          "& .MuiInputBase-input": {
            fontFamily: "Inter, sans-serif",
          },
          "& .MuiFormHelperText-root": {
            fontFamily: "Inter, sans-serif",
          },
        }}
      />
      <Dialog open={open} onClose={handleClickClose} maxWidth="md" fullWidth>
        <Box sx={{ p: 3 }}>
          <Box sx={{ mb: 3 }}>
            <DialogTitle
              sx={{
                p: 0,
                fontFamily: "Lexend, sans-serif",
                fontSize: "1.5rem",
                fontWeight: "bold",
                color: "#333",
              }}
            >
              Registrar nuevo servicio
            </DialogTitle>
            <DialogContentText
              sx={{
                p: 0,
                mt: 1,
                fontFamily: "Inter, sans-serif",
                color: "#666",
              }}
            >
              Complete la información del nuevo servicio a registrar.
            </DialogContentText>
          </Box>

          <IconButton
            aria-label="close"
            onClick={(event) => handleClickClose(event, "closeButtonClick")}
            sx={{
              position: "absolute",
              right: 16,
              top: 16,
            }}
          >
            <CloseIcon />
          </IconButton>

          <Box component="form" onSubmit={handleSubmit}>
            <Grid container spacing={2}>
              {/* Primera fila: Servicios y SubServicios */}
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" sx={labelStyles}>
                  Servicios
                </Typography>
                <FormControl fullWidth error={Boolean(error.serviceName)}>
                  <Select
                    name="serviceName"
                    value={formData.serviceName}
                    onChange={(event: SelectChangeEvent<string>) => {
                      setFormData((prev) => ({
                        ...prev,
                        serviceName: event.target.value,
                        subServiceName: "",
                      }));
                    }}
                    displayEmpty
                    inputRef={selectServicioRef}
                    sx={{
                      height: "56px",
                      "& .MuiSelect-select": {
                        fontFamily: "Inter, sans-serif",
                      },
                      "& .MuiFormHelperText-root": {
                        fontFamily: "Inter, sans-serif",
                      },
                    }}
                  >
                    <MenuItem value="" disabled>
                      <Typography sx={{ fontFamily: "Inter, sans-serif" }}>
                        Seleccione un servicio
                      </Typography>
                    </MenuItem>
                    {serviciosData.map((servicio) => (
                      <MenuItem
                        key={servicio.categoria}
                        value={servicio.categoria}
                      >
                        <Typography sx={{ fontFamily: "Inter, sans-serif" }}>
                          {servicio.categoria}
                        </Typography>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="body2" sx={labelStyles}>
                  SubServicios
                </Typography>
                <FormControl fullWidth error={Boolean(error.subServiceName)}>
                  <Select
                    name="subServiceName"
                    value={formData.subServiceName}
                    onChange={(event: SelectChangeEvent<string>) => {
                      setFormData((prev) => ({
                        ...prev,
                        subServiceName: event.target.value,
                      }));
                    }}
                    displayEmpty
                    inputRef={selectSubServicioRef}
                    sx={{
                      height: "56px",
                      "& .MuiSelect-select": {
                        fontFamily: "Inter, sans-serif",
                      },
                      "& .MuiFormHelperText-root": {
                        fontFamily: "Inter, sans-serif",
                      },
                    }}
                  >
                    <MenuItem value="" disabled>
                      <Typography sx={{ fontFamily: "Inter, sans-serif" }}>
                        Seleccione un subservicio
                      </Typography>
                    </MenuItem>
                    {serviciosData
                      .find((s) => s.categoria === formData.serviceName)
                      ?.subServicios.map((sub) => (
                        <MenuItem key={sub.nombre} value={sub.nombre}>
                          <Typography sx={{ fontFamily: "Inter, sans-serif" }}>
                            {sub.nombre}
                          </Typography>
                        </MenuItem>
                      ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Segunda fila: Monto Relativo */}
              <Grid item xs={12}>
                <Typography variant="body2" sx={labelStyles}>
                  Monto Relativo
                </Typography>
                <FormattedInput
                  value={formData.subServicePrice}
                  onChange={handleAmountChange}
                  fullWidth
                  placeholder="Ejemplo: 1.236.365,12"
                  sx={{
                    "& .MuiInputBase-input": {
                      fontFamily: "Inter, sans-serif",
                    },
                    "& .MuiFormHelperText-root": {
                      fontFamily: "Inter, sans-serif",
                    },
                  }}
                />
              </Grid>

              {/* Tercera fila: Unidad de medida */}
              <Grid item xs={12}>
                <Typography variant="body2" sx={labelStyles}>
                  Unidad de medida
                </Typography>
                <Grid container spacing={2}>
                  {[
                    { value: "FARDO", label: "FARDO" },
                    { value: "HECTAREAS", label: "HECTAREAS" },
                    { value: "HORAS", label: "HORAS" },
                    { value: "MEGAFARDOS", label: "MEGAFARDOS" },
                    { value: "METROS", label: "METROS" },
                    { value: "ROLLOS", label: "ROLLOS" },
                  ].map((button) => (
                    <Grid item xs={12} sm={4} key={button.value}>
                      <Button
                        fullWidth
                        variant={
                          formData.subServiceUnite === button.value
                            ? "contained"
                            : "outlined"
                        }
                        onClick={() => handleAmountChange(button.value)}
                        sx={{
                          height: "56px",
                          backgroundColor:
                            formData.subServiceUnite === button.value
                              ? "#1976d2"
                              : "transparent",
                          color:
                            formData.subServiceUnite === button.value
                              ? "white"
                              : "inherit",
                          "&:hover": {
                            backgroundColor:
                              formData.subServiceUnite === button.value
                                ? "#1565c0"
                                : "rgba(25, 118, 210, 0.04)",
                          },
                          fontFamily: "Inter, sans-serif", // Agregado font-family Inter
                          textTransform: "none", // Para mantener el caso original del texto
                        }}
                      >
                        {button.label}
                      </Button>
                    </Grid>
                  ))}
                </Grid>
              </Grid>
            </Grid>

            {/* Botones de acción */}
            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                gap: 2,
                mt: 3,
              }}
            >
              <Button
                onClick={handleClickClose}
                variant="outlined"
                sx={{ fontFamily: "Inter, sans-serif" }}
              >
                CANCELAR
              </Button>
              <Button
                type="submit"
                variant="contained"
                startIcon={<AddCircleIcon />}
                sx={{
                  bgcolor: "#2E7D32",
                  color: "#ffffff",
                  "&:hover": { bgcolor: "#1B5E20" },
                  textTransform: "none",
                  "& .MuiSvgIcon-root": {
                    color: "#ffffff",
                  },
                }}
              >
                {estadoModal === "add" ? "Registrar" : "Guardar"}
              </Button>
            </Box>
          </Box>
        </Box>
      </Dialog>

      <Paper elevation={2} sx={{ p: 2, mb: 3, borderRadius: 2 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{ borderBottom: 1, borderColor: "divider", mb: 2 }}
        >
          {serviciosData.map((servicio, index) => (
            <Tab
              key={index}
              label={
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography
                    sx={{
                      textTransform: "capitalize",
                      fontSize: "0.875rem",
                      fontFamily: "Lexend, sans-serif",
                    }}
                  >
                    {servicio.categoria}
                  </Typography>
                </Box>
              }
              sx={{ textTransform: "none" }}
            />
          ))}
        </Tabs>

        {serviciosData.map((servicio, index) => (
          <div key={index} role="tabpanel" hidden={tabValue !== index}>
            {tabValue === index && (
              <Box sx={{ p: 2 }}>
                <Typography
                  variant="h6"
                  sx={{
                    mb: 3,
                    fontFamily: "Lexend, sans-serif",
                  }}
                >
                  Subservicios de {servicio.categoria}
                </Typography>
                <Grid container spacing={2}>
                  {isLoading ? (
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "center",
                        width: "100%",
                        mt: 3,
                      }}
                    >
                      <CircularProgress />
                    </Box>
                  ) : (
                    rows
                      .filter((row) => row.servcio.includes(servicio.categoria))
                      .map((servicioData, subIndex) => (
                        <Grid item xs={12} sm={6} md={4} key={subIndex}>
                          <Card
                            sx={{
                              cursor: "pointer",
                              height: "100%",
                              backgroundColor: "background.paper",
                              borderRadius: "8px",
                              boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                              transition: "all 0.3s ease",
                              "&:hover": {
                                transform: "translateY(-4px)",
                                boxShadow: "0 4px 8px rgba(0,0,0,0.2)",
                              },
                              border: "1px solid #e0e0e0",
                              position: "relative", // Para posicionar el IconButton
                            }}
                          >
                            <IconButton
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEdit(servicioData.id);
                              }}
                              sx={{
                                position: "absolute",
                                top: 8,
                                right: 8,
                                backgroundColor: "rgba(255, 255, 255, 0.9)",
                                "&:hover": {
                                  backgroundColor: "rgba(255, 255, 255, 1)",
                                },
                              }}
                            >
                              <EditIcon color="primary" />
                            </IconButton>
                            <CardHeader
                              avatar={
                                <Avatar
                                  sx={{
                                    bgcolor: "#f5f5f5",
                                    color: "#1976d2",
                                    width: 48,
                                    height: 48,
                                  }}
                                >
                                  <AgricultureIcon sx={{ fontSize: 24 }} />
                                </Avatar>
                              }
                              title={
                                <Typography
                                  variant="h6"
                                  sx={{
                                    fontWeight: 600,
                                    fontSize: "1.1rem",
                                    color: "#2c3e50",
                                    mb: 0.5,
                                  }}
                                >
                                  {servicioData.servcio.split(" - ").pop()}
                                </Typography>
                              }
                              subheader={
                                <Typography
                                  variant="body2"
                                  sx={{
                                    color: "#7f8c8d",
                                    fontSize: "0.9rem",
                                  }}
                                >
                                  {servicio.categoria}
                                </Typography>
                              }
                            />
                            <CardContent>
                              <Box
                                sx={{
                                  mt: 1,
                                  p: 2,
                                  backgroundColor: "#f8f9fa",
                                  borderRadius: "8px",
                                  display: "flex",
                                  justifyContent: "space-between",
                                  alignItems: "center",
                                }}
                              >
                                <Box>
                                  <Typography
                                    variant="h6"
                                    sx={{
                                      color: "#2ecc71",
                                      fontWeight: 700,
                                      fontSize: "1.2rem",
                                    }}
                                  >
                                    ${servicioData.monto.toLocaleString()}
                                  </Typography>
                                  {servicioData.rindeQQ && (
                                    <Typography
                                      variant="body2"
                                      sx={{
                                        color: "#34495e",
                                        mt: 0.5,
                                        fontWeight: 500,
                                      }}
                                    >
                                      {servicioData.rindeQQ} QQ/ha
                                    </Typography>
                                  )}
                                </Box>
                                <Chip
                                  label={
                                    servicioData.unidad === "10"
                                      ? "Fardo"
                                      : servicioData.unidad === "20"
                                      ? "Hectáreas"
                                      : servicioData.unidad === "30"
                                      ? "Horas"
                                      : servicioData.unidad === "40"
                                      ? "MegaFardos"
                                      : servicioData.unidad === "50"
                                      ? "Metros"
                                      : "Rollos"
                                  }
                                  sx={{
                                    backgroundColor: "#e3f2fd",
                                    color: "#1976d2",
                                    fontWeight: 500,
                                  }}
                                />
                              </Box>
                            </CardContent>
                          </Card>
                        </Grid>
                      ))
                  )}
                  {!isLoading &&
                    rows.filter((row) =>
                      row.servcio.includes(servicio.categoria)
                    ).length === 0 && (
                      <Box
                        sx={{
                          width: "100%",
                          textAlign: "center",
                          mt: 3,
                          p: 3,
                          bgcolor: "#f5f5f5",
                          borderRadius: 2,
                        }}
                      >
                        <Typography
                          variant="h6"
                          color="text.secondary"
                          sx={{
                            fontFamily: "Inter, sans-serif",
                          }}
                        >
                          No hay servicios disponibles en esta categoría
                        </Typography>
                      </Box>
                    )}
                </Grid>
              </Box>
            )}
          </div>
        ))}
      </Paper>
    </>
  );
};

export default Servicios;
