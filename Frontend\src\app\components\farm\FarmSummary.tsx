"use client";
import React, { useEffect, useState } from "react";
import { Box, Typography, Card, Link as <PERSON><PERSON><PERSON><PERSON>, Chip, Skeleton } from "@mui/material";
import Link from "next/link";
import HomeIcon from '@mui/icons-material/Home';
import PersonIcon from '@mui/icons-material/Person';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import { styled } from "@mui/material/styles";

interface Persona {
  id: string;
  razonSocial: string;
}

interface Establecimiento {
  id: string;
  nombre: string;
  lugar: string;
  persona: Persona;
}

const StyledContainer = styled(Card)(({ theme }) => ({
  padding: theme.spacing(3),
  backgroundColor: "#ffffff",
  borderRadius: "12px",
  border: "1px solid #E5E7EB",
  boxShadow: "0 4px 12px rgba(0, 0, 0, 0.05)",
  marginBottom: theme.spacing(2),
  transition: "all 0.3s ease",
  "&:hover": {
    boxShadow: "0 6px 16px rgba(0, 0, 0, 0.08)",
  }
}));

const HeaderBox = styled(Box)(({ theme }) => ({
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  paddingBottom: theme.spacing(2),
  marginBottom: theme.spacing(2),
  borderBottom: "1px solid #eaeaea",
}));

const CustomLink = styled(MuiLink)(({ theme }) => ({
  color: theme.palette.primary.main,
  textDecoration: "none",
  display: "flex",
  alignItems: "center",
  fontWeight: 500,
  fontSize: "0.875rem",
  transition: "color 0.2s ease",
  fontFamily: "Inter, sans-serif",
  "&:hover": {
    color: theme.palette.primary.dark,
  },
}));

const EstablecimientoCard = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  borderRadius: "8px",
  marginBottom: theme.spacing(2),
  transition: "all 0.2s ease",
  backgroundColor: "#f9fafb",
  "&:hover": {
    backgroundColor: "#f0f4ff",
    transform: "translateY(-2px)",
  },
  "&:last-child": {
    marginBottom: 0,
  }
}));

const LocationChip = styled(Chip)(({ theme }) => ({
  backgroundColor: "#fff0f0",
  color: "#e53935",
  fontFamily: "Inter, sans-serif",
  fontWeight: 500,
  fontSize: "0.75rem",
  height: "24px",
  "& .MuiChip-icon": {
    color: "#e53935",
  }
}));

const FarmSummary: React.FC = () => {
  const [establecimientos, setEstablecimientos] = useState<Establecimiento[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchEstablecimientos = async () => {
      try {
        setLoading(true);
        const response = await fetch(
          "http://localhost:8080/api/establecimiento"
        );
        if (!response.ok) {
          throw new Error("Error al obtener establecimientos");
        }
        const data = await response.json();
        setEstablecimientos(data);
      } catch (error) {
        console.error("Error fetching establecimientos:", error);
        setEstablecimientos([]);
      } finally {
        setLoading(false);
      }
    };

    fetchEstablecimientos();
  }, []);

  return (
    <StyledContainer>
      <HeaderBox>
        <Typography 
          variant="h6" 
          component="h2"
          sx={{ 
            fontFamily: "Lexend, sans-serif",
            fontWeight: 600,
            fontSize: "1.125rem",
            color: "#111827"
          }}
        >
          Resumen de Establecimientos
        </Typography>
        <Link href="/establecimiento" passHref>
          <CustomLink>
            Ver todos
            <ArrowForwardIcon sx={{ ml: 0.5, fontSize: "1rem" }} />
          </CustomLink>
        </Link>
      </HeaderBox>

      <Box>
        {loading ? (
          // Skeleton loading state
          Array.from(new Array(2)).map((_, index) => (
            <Box key={index} sx={{ mb: 2 }}>
              <Skeleton variant="rectangular" height={100} sx={{ borderRadius: 2 }} />
            </Box>
          ))
        ) : establecimientos.length === 0 ? (
          <Box 
            sx={{ 
              py: 4, 
              textAlign: "center",
              backgroundColor: "#f9fafb",
              borderRadius: "8px"
            }}
          >
            <Typography
              variant="body1"
              color="text.secondary"
              sx={{ fontFamily: "Inter, sans-serif" }}
            >
              No hay establecimientos registrados
            </Typography>
          </Box>
        ) : (
          establecimientos.map((establecimiento) => (
            <EstablecimientoCard key={establecimiento.id}>
              <Box sx={{ 
                display: 'flex', 
                justifyContent: 'space-between',
                alignItems: 'flex-start',
                flexWrap: 'wrap',
                gap: 1
              }}>
                {/* Información principal */}
                <Box sx={{ flex: 1, minWidth: '200px' }}>
                  <Box sx={{ 
                    display: 'flex', 
                    alignItems: 'center',
                    mb: 1.5
                  }}>
                    <HomeIcon 
                      sx={{ 
                        mr: 1.5, 
                        color: "#3b82f6", 
                        fontSize: "22px"
                      }} 
                    />
                    <Typography
                      variant="subtitle1"
                      sx={{
                        fontWeight: 600,
                        color: "#111827",
                        fontFamily: "Lexend, sans-serif",
                        fontSize: "1rem"
                      }}
                    >
                      {establecimiento.nombre || "Sin nombre"}
                    </Typography>
                  </Box>
                  
                  <Box sx={{ 
                    display: 'flex', 
                    alignItems: 'center',
                    ml: '4px',
                    mb: 0.5
                  }}>
                    <PersonIcon 
                      sx={{ 
                        mr: 1.5, 
                        color: "#6b7280", 
                        fontSize: "18px"
                      }} 
                    />
                    <Typography
                      variant="body2"
                      sx={{
                        color: "#4b5563",
                        fontFamily: "Inter, sans-serif",
                        fontWeight: 500
                      }}
                    >
                      {establecimiento.persona?.razonSocial || "Sin propietario"}
                    </Typography>
                  </Box>
                </Box>

                {/* Ubicación */}
                <LocationChip
                  icon={<LocationOnIcon />}
                  label={establecimiento.lugar || "Sin ubicación"}
                  size="small"
                />
              </Box>
            </EstablecimientoCard>
          ))
        )}
      </Box>
    </StyledContainer>
  );
};

export default FarmSummary;














