
package agro.personas.modeloEntidades;

import java.util.List;
import java.util.UUID;

import com.fasterxml.jackson.annotation.JsonManagedReference;

import agro.establecimientos.modeloEntidades.EstablecimientoEntidad;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity(name = "agricultor")
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PersonaEntidad {

    @Id
    @GeneratedValue
    private UUID id;

    private String razonSocial;
    private String tipoCliente; // 'Productor', 'Empresa', 'Cooperativa', 'Estancia'
    private String nombreContacto;
    private String cargoContacto;
    private String direccion;
    private String telefono;
    private String mail;
    private String lugar;
    private String provincia;
    private String condFrenteIva;
    private String documento;

    @OneToMany(mappedBy = "persona", cascade = CascadeType.ALL)
    @JsonManagedReference
    private List<EstablecimientoEntidad> establecimientos;

}
