import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import * as React from "react";
import MenuPrincipal from "../components/menu/MenuPrincipal";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="es">
      <body className={inter.className}>
        <MenuPrincipal>{children}</MenuPrincipal>
      </body>
    </html>
  );
}
